╔════════════════════════════════════════════════════════════════════════════╗
║              CHANGE USB MODE VIA SETTINGS (NO NOTIFICATION)                ║
╚════════════════════════════════════════════════════════════════════════════╝

PROBLEM:
  No USB notification appearing on tablet screen


SOLUTION: USE SETTINGS MENU DIRECTLY
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

STEP 1: OPEN SETTINGS
──────────────────

  On your TABLET:
  
  1. Tap the Settings app (⚙️ icon)
  2. Or swipe down and look for Settings icon
  3. Or go to: Home → Apps → Settings


STEP 2: FIND USB SETTINGS
────────────────────────

  In Settings, look for ONE of these:
  
  ✓ "Connected devices"
  ✓ "Device connection"
  ✓ "USB"
  ✓ "Storage"
  ✓ "About device" → "USB"
  
  Scroll down if you don't see it immediately.


STEP 3: FIND USB CONNECTION MODE
────────────────────────────────

  Once you find the USB section, look for:
  
  ✓ "USB connection mode"
  ✓ "USB preferences"
  ✓ "USB mode"
  ✓ "USB connection"
  
  Tap on it.


STEP 4: SELECT FILE TRANSFER
────────────────────────────

  You should see options:
  
  ○ Charging only
  ● File transfer (MTP)     ← SELECT THIS
  ○ PTP
  ○ MIDI
  
  Tap on "File transfer" or "MTP"


STEP 5: CONFIRM
──────────────

  Tap [OK] or [Done] or [Save]
  
  The setting should be saved.


VERIFY ON LINUX
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  On your LINUX COMPUTER:
  
  $ adb devices
  
  Expected output:
  
  List of devices attached
  XXXXXXXXXXXXXXXX    device
  
  ✓ If it shows "device" → SUCCESS!


SAMSUNG TABLET SPECIFIC PATHS
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  For Samsung tablets, try these paths:
  
  Path 1:
  Settings → Connected devices → USB → USB connection mode
  
  Path 2:
  Settings → Device connection → USB → USB connection mode
  
  Path 3:
  Settings → Storage → USB connection mode
  
  Path 4:
  Settings → About device → USB → USB connection mode
  
  Path 5:
  Settings → System → USB → USB connection mode


VISUAL GUIDE
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  TABLET SCREEN:
  
  ┌─────────────────────────────────────┐
  │ Settings                            │
  │                                     │
  │ ⚙️ Connected devices                │
  │ 📱 Device connection                │
  │ 🔌 USB                              │
  │ 💾 Storage                          │
  │ ℹ️  About device                     │
  │                                     │
  └─────────────────────────────────────┘
  
  Tap on "Connected devices" or "USB"
  
  ┌─────────────────────────────────────┐
  │ Connected devices                   │
  │                                     │
  │ 🔌 USB connection mode              │
  │    Charging only                    │
  │                                     │
  │ 🔋 Battery                          │
  │ 📡 Bluetooth                        │
  │                                     │
  └─────────────────────────────────────┘
  
  Tap on "USB connection mode"
  
  ┌─────────────────────────────────────┐
  │ USB connection mode                 │
  │                                     │
  │ ○ Charging only                     │
  │ ● File transfer (MTP)               │
  │ ○ PTP                               │
  │ ○ MIDI                              │
  │                                     │
  │ [OK]                                │
  │                                     │
  └─────────────────────────────────────┘
  
  Select "File transfer" and tap [OK]


TROUBLESHOOTING
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  ❌ Can't find USB settings?
     - Try scrolling down in Settings
     - Look under "Connected devices"
     - Look under "Device connection"
     - Try "About device"

  ❌ Device still not detected?
     - Disconnect USB cable
     - Wait 5 seconds
     - Reconnect USB cable
     - Run: adb kill-server && adb start-server

  ❌ Still showing "Charging only"?
     - Make sure you tapped [OK] or [Done]
     - Try restarting tablet
     - Try different USB cable


QUICK TEST
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  Once device shows as "device":
  
  $ adb shell getprop ro.build.model
  $ adb shell ls /sdcard/
  $ python3 tablet_usb_helper.py


IMPORTANT NOTES
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  ⚠️  The USB mode setting is on the TABLET, not on your computer!
  ⚠️  You must physically change it on your tablet screen
  ⚠️  Make sure to tap [OK] or [Done] to save the change
  ⚠️  Different Samsung models may have slightly different menus


STILL NEED HELP?
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  Read: NO_USB_NOTIFICATION.md
  Read: CHANGE_USB_MODE.md
  Run:  ./diagnose_tablet.sh

╚════════════════════════════════════════════════════════════════════════════╝

