{"name": "scrape_news", "description": "Scrapes the latest news headlines from Raw Story or other news websites and returns them in a formatted list.", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "The news website URL to scrape (optional, defaults to rawstory.com)", "default": "https://rawstory.com"}}, "required": []}, "code": "import requests\nfrom bs4 import <PERSON><PERSON>oup\nfrom typing import Optional\n\ndef scrape_news(url: Optional[str] = \"https://rawstory.com\") -> str:\n    \"\"\"\n    Scrapes the latest news headlines from Raw Story or other news websites.\n    \n    :param url: The news website URL to scrape (optional, defaults to rawstory.com)\n    :return: Formatted list of news headlines\n    \"\"\"\n    try:\n        print(f\"[DEBUG] Scraping news from: {url}\")\n        \n        # Send an HTTP request to the URL\n        response = requests.get(url, timeout=10)\n        \n        # Check if the request was successful\n        if response.status_code != 200:\n            return f\"❌ Failed to retrieve page. Status code: {response.status_code}\"\n        \n        # Parse the HTML content using Beautiful Soup\n        soup = BeautifulSoup(response.content, 'html.parser')\n        \n        # Find all links on the webpage\n        links = soup.find_all('a')\n        \n        # Filter for article headlines\n        headlines = []\n        navigation_keywords = [\n            \"Home\", \"Trump\", \"U.S. News\", \"World\", \"Science\", \"Video\", \"Opinion\", \n            \"Investigations\", \"Ethics Policy\", \"About us\", \"Games\", \"HELP\", \n            \"get the newsletter\", \"JOIN RAW STORY\", \"Masthead\", \"Privacy Policy\", \n            \"Manage Preferences\", \"Debug Logs\", \"MORE\", \"Raw Exclusives\",\n            \"Frontpage Commentary\", \"Also Read\", \"SmartNews\", \"Super Head\",\n            \"Trump News\", \"Media\"\n        ]\n        \n        seen_headlines = set()  # To avoid duplicates\n        \n        for link in links:\n            text = link.get_text().strip().replace('\\n', ' ').replace('\\r', '')\n            href = link.get('href')\n            \n            # Only process links with both text and href\n            if not text or not href:\n                continue\n                \n            # Skip navigation/footer links\n            if any(keyword in text for keyword in navigation_keywords):\n                continue\n                \n            # Skip short navigation-style text (likely not article headlines)\n            if len(text) < 20:\n                continue\n                \n            # Skip mailto links and javascript links\n            if href.startswith('mailto:') or href.startswith('javascript:'):\n                continue\n                \n            # Skip relative links that are likely navigation (like \"/\", \"/st/\", etc.)\n            if href.startswith('/') and len(href) < 20:\n                continue\n                \n            # Look for article-like URLs (contain year/article ID patterns)\n            if '/2674' in href or 'rawstory.com' in href:\n                # This looks like an article headline\n                if len(text) > 100:\n                    text = text[:100] + \"...\"\n                \n                # Avoid duplicates\n                if text not in seen_headlines:\n                    headlines.append(text)\n                    seen_headlines.add(text)\n        \n        # Format the headlines nicely\n        if not headlines:\n            return \"📰 No news headlines found.\"\n        \n        result = f\"📰 **Latest News Headlines from {url}:**\\n\\n\"\n        \n        for i, headline in enumerate(headlines[:12], 1):  # Limit to 12 headlines\n            result += f\"{i}. {headline}\\n\\n\"\n        \n        result += f\"*Found {len(headlines[:12])} headlines*\"\n        \n        print(f\"[DEBUG] Successfully scraped {len(headlines)} headlines\")\n        return result\n        \n    except requests.exceptions.RequestException as e:\n        error_msg = f\"🌐 Network error: {str(e)}\"\n        print(f\"[DEBUG] {error_msg}\")\n        return error_msg\n    except Exception as e:\n        error_msg = f\"❌ Error scraping news: {str(e)}\"\n        print(f\"[DEBUG] {error_msg}\")\n        return error_msg\n\nreturn scrape_news(url)"}