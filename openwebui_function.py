"""
title: News Scraper
author: Jesus
version: 1.0
description: Scrapes latest news headlines from Raw Story and other news websites
"""

import requests
from bs4 import BeautifulSoup
from typing import Optional

class Tools:
    def __init__(self):
        pass

    def scrape_news(self, url: Optional[str] = "https://rawstory.com") -> str:
        """
        Scrapes the latest news headlines from Raw Story or other news websites.
        
        Args:
            url (str): The news website URL to scrape (optional, defaults to rawstory.com)
            
        Returns:
            str: Formatted list of news headlines
        """
        try:
            # Send an HTTP request to the URL
            response = requests.get(url, timeout=10)
            
            if response.status_code != 200:
                return f"❌ Failed to retrieve page. Status code: {response.status_code}"
            
            # Parse the HTML content
            soup = BeautifulSoup(response.content, 'html.parser')
            links = soup.find_all('a')
            
            # Filter for article headlines
            headlines = []
            navigation_keywords = [
                "Home", "Trump", "U.S. News", "World", "Science", "Video", "Opinion", 
                "Investigations", "Ethics Policy", "About us", "Games", "HELP", 
                "get the newsletter", "JOIN RAW STORY", "Masthead", "Privacy Policy", 
                "Manage Preferences", "Debug Logs", "MORE", "Raw Exclusives",
                "Frontpage Commentary", "Also Read", "SmartNews", "Super Head",
                "Trump News", "Media"
            ]
            
            seen_headlines = set()
            
            for link in links:
                text = link.get_text().strip().replace('\n', ' ').replace('\r', '')
                href = link.get('href')
                
                if not text or not href:
                    continue
                    
                if any(keyword in text for keyword in navigation_keywords):
                    continue
                    
                if len(text) < 20:
                    continue
                    
                if href.startswith('mailto:') or href.startswith('javascript:'):
                    continue
                    
                if href.startswith('/') and len(href) < 20:
                    continue
                    
                if '/2674' in href or 'rawstory.com' in href:
                    if len(text) > 100:
                        text = text[:100] + "..."
                    
                    if text not in seen_headlines:
                        headlines.append(text)
                        seen_headlines.add(text)
            
            if not headlines:
                return "📰 No news headlines found."
            
            result = f"📰 **Latest News Headlines:**\n\n"
            
            for i, headline in enumerate(headlines[:10], 1):
                result += f"{i}. {headline}\n\n"
            
            result += f"*Found {len(headlines[:10])} headlines*"
            return result
            
        except Exception as e:
            return f"❌ Error scraping news: {str(e)}"

    def test_function(self, message: str = "Hello!") -> str:
        """
        Simple test function to verify the tool is working.
        
        Args:
            message (str): Test message to return
            
        Returns:
            str: Confirmation message
        """
        import datetime
        now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        return f"✅ Function is working! Time: {now}\nMessage: {message}"
