"""
Open WebUI Functions
News scraper and other tools for Open WebUI
"""

import requests
from bs4 import BeautifulSoup
from typing import Optional
import json

class Tools:
    def __init__(self):
        pass

    def scrape_news(self, url: Optional[str] = "https://rawstory.com") -> str:
        """
        Scrapes the latest news headlines from Raw Story or other news websites.
        
        :param url: The news website URL to scrape (optional, defaults to rawstory.com)
        :return: Formatted list of news headlines
        """
        try:
            # Send an HTTP request to the URL
            response = requests.get(url, timeout=10)
            
            # Check if the request was successful
            if response.status_code != 200:
                return f"Failed to retrieve page. Status code: {response.status_code}"
            
            # Parse the HTML content using Beautiful Soup
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find all links on the webpage
            links = soup.find_all('a')
            
            # Filter for article headlines
            headlines = []
            navigation_keywords = [
                "Home", "Trump", "U.S. News", "World", "Science", "Video", "Opinion", 
                "Investigations", "Ethics Policy", "About us", "Games", "HELP", 
                "get the newsletter", "JOIN RAW STORY", "Masthead", "Privacy Policy", 
                "Manage Preferences", "Debug Logs", "MORE", "Raw Exclusives",
                "Frontpage Commentary", "Also Read", "SmartNews", "Super Head",
                "Trump News", "Media"
            ]
            
            seen_headlines = set()  # To avoid duplicates
            
            for link in links:
                text = link.get_text().strip().replace('\n', ' ').replace('\r', '')
                href = link.get('href')
                
                # Only process links with both text and href
                if not text or not href:
                    continue
                    
                # Skip navigation/footer links
                if any(keyword in text for keyword in navigation_keywords):
                    continue
                    
                # Skip short navigation-style text (likely not article headlines)
                if len(text) < 20:
                    continue
                    
                # Skip mailto links and javascript links
                if href.startswith('mailto:') or href.startswith('javascript:'):
                    continue
                    
                # Skip relative links that are likely navigation (like "/", "/st/", etc.)
                if href.startswith('/') and len(href) < 20:
                    continue
                    
                # Look for article-like URLs (contain year/article ID patterns)
                if '/2674' in href or 'rawstory.com' in href:
                    # This looks like an article headline
                    if len(text) > 100:
                        text = text[:100] + "..."
                    
                    # Avoid duplicates
                    if text not in seen_headlines:
                        headlines.append(text)
                        seen_headlines.add(text)
            
            # Format the headlines nicely
            if not headlines:
                return "No news headlines found."
            
            result = f"**Latest News Headlines from {url}:**\n\n"
            
            for i, headline in enumerate(headlines[:15], 1):  # Limit to 15 headlines
                result += f"{i}. {headline}\n\n"
            
            result += f"\n*Found {len(headlines[:15])} headlines*"
            
            return result
            
        except requests.exceptions.RequestException as e:
            return f"Network error: {str(e)}"
        except Exception as e:
            return f"Error scraping news: {str(e)}"

    def get_weather(self, city: str) -> str:
        """
        Retrieves the current weather for a given city.
        
        :param city: The city to get the weather for
        :return: Weather information for the city
        """
        try:
            # Placeholder for weather functionality
            return f"Weather information for {city} is not currently available. Please check your local weather service."
        except Exception as e:
            return f"Error getting weather for {city}: {str(e)}"

    def search_wikipedia(self, query: str) -> str:
        """
        Searches Wikipedia for a given query.
        
        :param query: The search query
        :return: Wikipedia search results
        """
        try:
            # Wikipedia API endpoint
            url = "https://en.wikipedia.org/api/rest_v1/page/summary/" + query.replace(" ", "_")
            
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                title = data.get('title', 'No title')
                extract = data.get('extract', 'No summary available')
                page_url = data.get('content_urls', {}).get('desktop', {}).get('page', '')
                
                result = f"**{title}**\n\n{extract}"
                if page_url:
                    result += f"\n\nRead more: {page_url}"
                
                return result
            else:
                return f"No Wikipedia article found for '{query}'"
                
        except Exception as e:
            return f"Error searching Wikipedia for '{query}': {str(e)}"

# Create tools instance
tools = Tools()

# Export functions for Open WebUI
def scrape_news(url: Optional[str] = "https://rawstory.com") -> str:
    """Scrape news headlines from a website"""
    return tools.scrape_news(url)

def get_weather(city: str) -> str:
    """Get weather for a city"""
    return tools.get_weather(city)

def search_wikipedia(query: str) -> str:
    """Search Wikipedia"""
    return tools.search_wikipedia(query)
