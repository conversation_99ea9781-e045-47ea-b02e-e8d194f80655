# How to Authorize Tablet Connection

## Step-by-Step Authorization Guide

### Step 1: Connect USB Cable
1. **Plug the USB cable** into your tablet
2. **Plug the other end** into your Linux computer
3. **Wait 5-10 seconds** for the system to recognize the device

### Step 2: Check for Authorization Dialog on Tablet

When you connect the tablet, a dialog should appear on the **tablet screen** asking:

**"Allow USB debugging?"**

or

**"Allow access to device data?"**

The dialog will show:
- Your computer's RSA key fingerprint
- Options to "Allow" or "Deny"
- Possibly a checkbox for "Always allow from this computer"

### Step 3: Authorize on Tablet

**On the tablet screen:**
1. Look for the authorization dialog
2. Tap **"Allow"** or **"OK"** button
3. Optionally check **"Always allow from this computer"** to skip this step next time
4. Confirm

### Step 4: Verify on Linux

After authorizing on the tablet, run on your Linux machine:

```bash
adb devices
```

You should see:
```
List of devices attached
XXXXXXXXXXXXXXXX    device
```

If it shows `unauthorized`, the tablet hasn't authorized yet. Go back to Step 2.

---

## Troubleshooting Authorization

### Problem: No Dialog Appears on Tablet

**Solution 1: Reconnect the cable**
```bash
# Disconnect and reconnect USB cable
# Wait 5-10 seconds
adb devices
```

**Solution 2: Restart ADB**
```bash
adb kill-server
adb start-server
adb devices
```

**Solution 3: Check USB Mode on Tablet**
On your tablet:
1. Settings → Connected devices (or USB)
2. Look for "USB connection mode" or "USB preferences"
3. Select **"File Transfer"** or **"MTP"** mode
4. NOT "Charging only"

**Solution 4: Try Different USB Port**
- Unplug from current port
- Try a different USB port on your computer
- Wait 5-10 seconds
- Run `adb devices`

**Solution 5: Try Different USB Cable**
- Some cables only charge, not transfer data
- Try a different USB cable
- Make sure it's a data cable, not just a charging cable

### Problem: Device Shows "Unauthorized"

```bash
adb devices
# Output: XXXXXXXXXXXXXXXX    unauthorized
```

**Solution:**
1. Check tablet screen for authorization dialog
2. Tap "Allow"
3. Check "Always allow from this computer" (optional)
4. Run `adb devices` again

### Problem: Device Shows "Offline"

```bash
adb devices
# Output: XXXXXXXXXXXXXXXX    offline
```

**Solution:**
```bash
# Restart ADB daemon
adb kill-server
adb start-server
adb devices

# If still offline, restart tablet
# Then reconnect USB cable
```

---

## What the Authorization Dialog Looks Like

### Samsung Tablet Authorization Dialog

The dialog typically shows:

```
┌─────────────────────────────────────┐
│  Allow USB debugging?               │
│                                     │
│  The identity of this computer is:  │
│  RSA key fingerprint:               │
│  XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:... │
│                                     │
│  ☐ Always allow from this computer  │
│                                     │
│  [Cancel]  [Allow]                  │
└─────────────────────────────────────┘
```

### What to Do:
1. **Check the checkbox** (optional, but recommended)
   - This prevents the dialog from appearing next time
2. **Tap "Allow"**
   - This authorizes the connection

---

## After Authorization

Once authorized, you can:

```bash
# Check connection
adb devices

# Get device info
adb shell getprop ro.build.model

# Transfer files
adb push local_file.txt /sdcard/
adb pull /sdcard/remote_file.txt

# Open shell
adb shell

# Use Python helper
python3 tablet_usb_helper.py
```

---

## Quick Checklist

- [ ] USB cable connected to tablet
- [ ] USB cable connected to Linux computer
- [ ] Waited 5-10 seconds
- [ ] Developer Mode enabled on tablet
- [ ] USB Debugging enabled on tablet
- [ ] Authorization dialog appeared on tablet
- [ ] Tapped "Allow" on tablet
- [ ] `adb devices` shows device as "device" (not "unauthorized")

---

## If Still Not Working

Run the diagnostic script:

```bash
./diagnose_tablet.sh
```

This will check:
- USB connection
- ADB installation
- Device detection
- Kernel messages
- Udev rules
- User permissions

---

## Common Issues Summary

| Issue | Solution |
|-------|----------|
| No dialog on tablet | Reconnect cable, try different port/cable |
| Device "unauthorized" | Tap "Allow" on tablet dialog |
| Device "offline" | Restart ADB: `adb kill-server && adb start-server` |
| No device detected | Check USB mode on tablet (File Transfer, not Charging) |
| Permission denied | Create udev rules (see TABLET_USB_SETUP.md) |

---

## Next Steps

Once authorized:
1. Run `python3 tablet_usb_helper.py` to verify
2. Try transferring a file
3. Run shell commands
4. Integrate with your project

See **README_TABLET.md** for more information.

