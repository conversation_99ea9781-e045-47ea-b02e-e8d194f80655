#!/usr/bin/env python3
"""
Samsung Tablet USB Communication Helper
Provides utilities for communicating with Android devices via ADB
"""

import subprocess
import sys
from typing import Tuple, List, Optional


class TabletUSBHelper:
    """Helper class for Android tablet USB communication"""
    
    def __init__(self):
        self.adb_cmd = 'adb'
        self._check_adb_installed()
    
    def _check_adb_installed(self) -> bool:
        """Check if ADB is installed"""
        try:
            subprocess.run([self.adb_cmd, 'version'], 
                         capture_output=True, 
                         check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("ERROR: ADB not installed. Run: sudo apt-get install adb")
            sys.exit(1)
    
    def run_command(self, *args) -> Tuple[str, str, int]:
        """
        Run an ADB command
        Returns: (stdout, stderr, return_code)
        """
        try:
            result = subprocess.run([self.adb_cmd] + list(args),
                                  capture_output=True,
                                  text=True,
                                  timeout=10)
            return result.stdout, result.stderr, result.returncode
        except subprocess.TimeoutExpired:
            return "", "Command timed out", 1
        except Exception as e:
            return "", str(e), 1
    
    def get_devices(self) -> List[str]:
        """Get list of connected devices"""
        stdout, _, _ = self.run_command('devices')
        devices = []
        for line in stdout.split('\n')[1:]:
            if line.strip() and '\t' in line:
                device_id, status = line.split('\t')
                devices.append((device_id.strip(), status.strip()))
        return devices
    
    def is_device_connected(self) -> bool:
        """Check if any device is connected"""
        devices = self.get_devices()
        return any(status == 'device' for _, status in devices)
    
    def get_device_info(self) -> dict:
        """Get device information"""
        info = {}
        properties = [
            'ro.build.model',
            'ro.build.version.release',
            'ro.build.version.sdk',
            'ro.serialno',
            'ro.product.manufacturer'
        ]
        
        for prop in properties:
            stdout, _, _ = self.run_command('shell', 'getprop', prop)
            key = prop.split('.')[-1]
            info[key] = stdout.strip()
        
        return info
    
    def push_file(self, local_path: str, remote_path: str) -> bool:
        """Push file to device"""
        stdout, stderr, code = self.run_command('push', local_path, remote_path)
        if code == 0:
            print(f"✓ Pushed {local_path} to {remote_path}")
            return True
        else:
            print(f"✗ Failed to push file: {stderr}")
            return False
    
    def pull_file(self, remote_path: str, local_path: str) -> bool:
        """Pull file from device"""
        stdout, stderr, code = self.run_command('pull', remote_path, local_path)
        if code == 0:
            print(f"✓ Pulled {remote_path} to {local_path}")
            return True
        else:
            print(f"✗ Failed to pull file: {stderr}")
            return False
    
    def shell_command(self, command: str) -> Tuple[str, str]:
        """Execute shell command on device"""
        stdout, stderr, _ = self.run_command('shell', command)
        return stdout, stderr
    
    def restart_adb(self) -> bool:
        """Restart ADB daemon"""
        self.run_command('kill-server')
        self.run_command('start-server')
        return self.is_device_connected()
    
    def print_status(self):
        """Print connection status"""
        print("\n" + "="*50)
        print("TABLET USB CONNECTION STATUS")
        print("="*50)
        
        devices = self.get_devices()
        if not devices:
            print("✗ No devices found")
            print("\nTroubleshooting:")
            print("1. Check USB cable connection")
            print("2. Enable USB Debugging on tablet")
            print("3. Authorize the connection on tablet")
            print("4. Try: adb kill-server && adb start-server")
            return
        
        for device_id, status in devices:
            symbol = "✓" if status == "device" else "✗"
            print(f"{symbol} Device: {device_id} ({status})")
        
        if self.is_device_connected():
            print("\n✓ Device is connected!")
            print("\nDevice Information:")
            info = self.get_device_info()
            for key, value in info.items():
                print(f"  {key}: {value}")
        else:
            print("\n✗ Device is not ready (may be unauthorized)")
            print("Check tablet screen for authorization prompt")
        
        print("="*50 + "\n")


def main():
    """Main function for testing"""
    helper = TabletUSBHelper()
    helper.print_status()
    
    # Test commands
    if helper.is_device_connected():
        print("Testing shell command...")
        stdout, stderr = helper.shell_command('echo "Hello from tablet"')
        print(f"Output: {stdout}")


if __name__ == '__main__':
    main()

