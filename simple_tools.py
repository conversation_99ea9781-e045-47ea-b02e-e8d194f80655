#!/usr/bin/env python3
"""
Simple test tools for Open WebUI debugging
"""

import sys
import json

def test_tool(message: str = "Hello from tool!") -> str:
    """
    Simple test tool that just returns a message
    
    Args:
        message (str): Message to return
        
    Returns:
        str: The message
    """
    print(f"[DEBUG] test_tool called with message: {message}", file=sys.stderr)
    return f"✅ Tool working! Message: {message}"

def get_time() -> str:
    """
    Returns current time
    
    Returns:
        str: Current time
    """
    import datetime
    now = datetime.datetime.now()
    return f"🕐 Current time: {now.strftime('%Y-%m-%d %H:%M:%S')}"

def simple_news() -> str:
    """
    Simple news headlines (hardcoded for testing)
    
    Returns:
        str: Sample news headlines
    """
    print("[DEBUG] simple_news called", file=sys.stderr)
    
    headlines = [
        "Test headline 1: This is a sample news story",
        "Test headline 2: Another sample story for testing",
        "Test headline 3: Tool integration is working"
    ]
    
    result = "📰 **Sample News Headlines:**\n\n"
    for i, headline in enumerate(headlines, 1):
        result += f"{i}. {headline}\n\n"
    
    return result

# Tool registry
TOOLS = {
    "test_tool": test_tool,
    "get_time": get_time,
    "simple_news": simple_news
}

def main():
    """Test the tools locally"""
    print("Testing simple tools...")
    print(test_tool())
    print(get_time())
    print(simple_news())

if __name__ == "__main__":
    main()
