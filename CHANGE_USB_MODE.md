# Change USB Mode from Charging to File Transfer

## Quick Fix: Change USB Mode on Tablet

### Method 1: Using Notification Panel (Easiest)

1. **Look at your tablet screen** - You should see a USB notification at the top
2. **Swipe down** from the top to open the notification panel
3. **Look for a notification** that says:
   - "USB charging this device"
   - "Charging via USB"
   - "USB connected"
   - Or similar

4. **Tap on the USB notification**
5. A menu should appear with options:
   - ☐ Charging only
   - ☐ File Transfer (or MTP)
   - ☐ PTP
   - ☐ MIDI

6. **Select "File Transfer"** or **"MTP"** (NOT "Charging only")
7. **Done!** The tablet should now be in file transfer mode

---

### Method 2: Using Settings Menu

If the notification method doesn't work:

1. On your tablet, go to **Settings**
2. Look for one of these options:
   - **Connected devices**
   - **USB**
   - **USB connection**
   - **Storage**
   - **About device** → **USB**

3. Find the **USB connection mode** or **USB preferences** option
4. Change from **"Charging only"** to:
   - **"File Transfer"** (preferred)
   - **"MTP"** (Media Transfer Protocol)
   - **"PTP"** (Picture Transfer Protocol)

5. **Confirm the change**

---

### Method 3: For Samsung Tablets Specifically

1. **Settings** → **Connected devices** (or **Device connection**)
2. Tap **USB**
3. Select **"File transfer"** or **"MTP"**
4. Done!

---

## After Changing USB Mode

### On Your Linux Computer:

```bash
# Check if device is now detected
adb devices

# You should see:
# List of devices attached
# XXXXXXXXXXXXXXXX    device
```

If it still shows nothing:
```bash
# Restart ADB
adb kill-server
adb start-server
adb devices
```

---

## Verify Connection Works

Once in File Transfer mode:

```bash
# Get device info
adb shell getprop ro.build.model

# List files on tablet
adb shell ls /sdcard/

# Transfer a test file
echo "test" > test.txt
adb push test.txt /sdcard/
adb pull /sdcard/test.txt

# Check with Python helper
python3 tablet_usb_helper.py
```

---

## What Each USB Mode Does

| Mode | Purpose | Use For |
|------|---------|---------|
| **Charging only** | Just charges the tablet | Charging (no data transfer) |
| **File Transfer / MTP** | Transfer files | ADB, file transfer ✓ |
| **PTP** | Picture transfer | Some cameras, limited file transfer |
| **MIDI** | Musical instrument | MIDI devices only |

**For ADB and file transfer, use: File Transfer or MTP**

---

## Troubleshooting

### Still showing "Charging only"?

1. **Disconnect USB cable**
2. **Wait 5 seconds**
3. **Reconnect USB cable**
4. **Immediately check notification panel** - the dialog may appear briefly
5. **Tap the notification** and select "File Transfer"

### Device still not detected?

```bash
# Check USB devices
lsusb

# Check kernel messages
dmesg | tail -20

# Run diagnostics
./diagnose_tablet.sh
```

### Authorization dialog appears again?

- Tap "Allow"
- Check "Always allow from this computer"
- Confirm

---

## Quick Checklist

- [ ] USB cable connected
- [ ] Tablet screen shows USB notification
- [ ] Tapped USB notification
- [ ] Selected "File Transfer" or "MTP" mode
- [ ] `adb devices` shows device
- [ ] Device status is "device" (not "unauthorized")
- [ ] Can run `adb shell` commands

---

## Next Steps

Once in File Transfer mode and device shows as "device":

1. **Verify connection:**
   ```bash
   python3 tablet_usb_helper.py
   ```

2. **Transfer files:**
   ```bash
   adb push local_file.txt /sdcard/
   adb pull /sdcard/remote_file.txt
   ```

3. **Run commands:**
   ```bash
   adb shell ls /sdcard/
   ```

4. **Use Python helper:**
   ```python
   from tablet_usb_helper import TabletUSBHelper
   tablet = TabletUSBHelper()
   tablet.print_status()
   ```

---

## Still Having Issues?

1. Read: **TABLET_USB_SETUP.md**
2. Run: `./diagnose_tablet.sh`
3. Check: `dmesg | tail -20`
4. Try: Different USB port or cable

