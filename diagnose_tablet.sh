#!/bin/bash
# Tablet USB Connection Diagnostic Script

echo "=========================================="
echo "TABLET USB DIAGNOSTIC TOOL"
echo "=========================================="
echo ""

# Check if ADB is installed
echo "[1/6] Checking ADB installation..."
if command -v adb &> /dev/null; then
    echo "✓ ADB is installed"
    adb version | head -1
else
    echo "✗ ADB not found. Install with: sudo apt-get install adb"
    exit 1
fi
echo ""

# Check USB devices
echo "[2/6] Checking USB devices..."
if command -v lsusb &> /dev/null; then
    echo "USB Devices:"
    lsusb
    echo ""
    # Check for Samsung devices
    if lsusb | grep -i samsung &> /dev/null; then
        echo "✓ Samsung device detected in USB"
    else
        echo "⚠ No Samsung device detected in USB"
        echo "  Check: USB cable, tablet power, USB port"
    fi
else
    echo "✗ lsusb not found"
fi
echo ""

# Check ADB devices
echo "[3/6] Checking ADB devices..."
adb kill-server > /dev/null 2>&1
sleep 1
adb start-server > /dev/null 2>&1
sleep 1

devices=$(adb devices | grep -v "List of attached" | grep -v "^$")
if [ -z "$devices" ]; then
    echo "✗ No devices found via ADB"
    echo "  Troubleshooting:"
    echo "  1. Enable USB Debugging on tablet"
    echo "  2. Authorize the connection"
    echo "  3. Try different USB port"
    echo "  4. Try different USB cable"
else
    echo "✓ ADB devices found:"
    echo "$devices"
    
    # Check if device is authorized
    if echo "$devices" | grep -q "device$"; then
        echo "✓ Device is authorized and ready"
    elif echo "$devices" | grep -q "unauthorized"; then
        echo "✗ Device is unauthorized"
        echo "  Check tablet screen for authorization prompt"
    elif echo "$devices" | grep -q "offline"; then
        echo "✗ Device is offline"
        echo "  Try: adb kill-server && adb start-server"
    fi
fi
echo ""

# Check kernel messages
echo "[4/6] Checking kernel messages..."
echo "Recent USB events:"
dmesg | grep -i "usb" | tail -5
echo ""

# Check udev rules
echo "[5/6] Checking udev rules..."
if [ -f /etc/udev/rules.d/51-android.rules ]; then
    echo "✓ Android udev rules found"
    cat /etc/udev/rules.d/51-android.rules
else
    echo "⚠ Android udev rules not found"
    echo "  Create with: sudo nano /etc/udev/rules.d/51-android.rules"
    echo "  Add: SUBSYSTEM==\"usb\", ATTR{idVendor}==\"04e8\", MODE=\"0666\", GROUP=\"plugdev\""
fi
echo ""

# Check user groups
echo "[6/6] Checking user permissions..."
current_user=$(whoami)
if groups $current_user | grep -q "plugdev"; then
    echo "✓ User is in plugdev group"
else
    echo "⚠ User not in plugdev group"
    echo "  Add with: sudo usermod -a -G plugdev $current_user"
    echo "  Then log out and log back in"
fi
echo ""

# Summary
echo "=========================================="
echo "DIAGNOSTIC SUMMARY"
echo "=========================================="
echo ""
echo "If device is not detected:"
echo "1. Check USB cable and port"
echo "2. Enable USB Debugging on tablet"
echo "3. Authorize connection on tablet"
echo "4. Run: adb kill-server && adb start-server"
echo "5. Run this script again"
echo ""
echo "For more help, see TABLET_USB_SETUP.md"
echo "=========================================="

