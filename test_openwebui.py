#!/usr/bin/env python3

"""
Test script for openwebui_functions.py
"""

try:
    print("Testing openwebui_functions.py...")
    
    # Test import
    from openwebui_functions import Tools
    print("✅ Import successful!")
    
    # Create tools instance
    tools = Tools()
    print("✅ Tools instance created!")
    
    # Test Wikipedia function
    print("\n🔍 Testing Wikipedia search...")
    result = tools.search_wikipedia("Python")
    print(f"Result length: {len(result)}")
    print(f"First 200 chars: {result[:200]}...")
    
    # Test news scraper
    print("\n📰 Testing news scraper...")
    news_result = tools.scrape_news("https://rawstory.com")
    print(f"News result length: {len(news_result)}")
    print(f"First 200 chars: {news_result[:200]}...")
    
    print("\n✅ All tests completed successfully!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
