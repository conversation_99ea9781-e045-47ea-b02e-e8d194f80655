# Web Search Tool for Open WebUI

## Overview
This enhanced function provides web search capabilities for Open WebUI, including:
- **Web Search**: General web search using DuckDuckGo
- **News Search**: Targeted news search
- **News Scraping**: Scrape headlines from specific news sites
- **Quick Search**: Fast single-result search

## Features

### 🔍 Web Search Functions
1. **`web_search(query, num_results=5)`** - General web search
2. **`search_news(query, num_results=5)`** - News-focused search  
3. **`quick_search(query)`** - Single result search
4. **`scrape_news(url)`** - Scrape news from specific sites
5. **`test_function(message)`** - Test the tool functionality

### 🌟 Key Capabilities
- Uses DuckDuckGo API for reliable search results
- Fallback web scraping if API fails
- Formatted, readable results with links
- News-specific search optimization
- Error handling and timeout protection

## Installation in Open WebUI

### Step 1: Access Functions
1. Open Open WebUI at http://localhost:8080
2. Click on your profile (top right)
3. Go to **Admin Panel** → **Functions**

### Step 2: Upload Function
1. Click **"+ Create Function"**
2. Copy and paste the entire content of `openwebui_function.py`
3. Click **"Save"**

### Step 3: Enable Function
1. Toggle the function to **"Enabled"**
2. Set permissions as needed
3. Click **"Save"**

## Usage Examples

### In Open WebUI Chat:
```
web_search("latest AI developments", 5)
search_news("climate change", 3)
quick_search("Python tutorial")
scrape_news("https://rawstory.com")
test_function("Hello World")
```

### Expected Output:
- Formatted search results with titles and links
- Quick answers when available
- Related topics and sources
- Error messages if search fails

## Technical Details

### Dependencies
- `requests` - HTTP requests
- `beautifulsoup4` - HTML parsing
- `json` - JSON handling
- `urllib.parse` - URL encoding

### Search Sources
- **Primary**: DuckDuckGo Instant Answer API
- **Fallback**: DuckDuckGo HTML scraping
- **News**: Targeted news search queries

### Rate Limiting
- Built-in timeout protection (10 seconds)
- Reasonable result limits (max 10 results)
- Error handling for failed requests

## Troubleshooting

### Common Issues:
1. **No results found**: Try different search terms
2. **Timeout errors**: Check internet connection
3. **Function not available**: Ensure it's enabled in Admin Panel

### Debug Steps:
1. Test with `test_function("test")`
2. Try `quick_search("simple query")`
3. Check Open WebUI logs for errors

## Version History
- **v2.0**: Added web search capabilities
- **v1.0**: Basic news scraping only
