#!/usr/bin/env python3
"""
News Scraper Tool for Open WebUI
Scrapes article headlines from Raw Story website
"""

import requests
from bs4 import BeautifulSoup
import sys
import json

def scrape_news_headlines(url="https://rawstory.com"):
    """
    Scrape article headlines from a news website
    
    Args:
        url (str): The URL to scrape (defaults to rawstory.com)
    
    Returns:
        list: List of article headlines
    """
    try:
        # Send an HTTP request to the URL
        response = requests.get(url, timeout=10)
        
        # Check if the request was successful
        if response.status_code != 200:
            return [f"Failed to retrieve page. Status code: {response.status_code}"]
        
        # Parse the HTML content using Beautiful Soup
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Find all links on the webpage
        links = soup.find_all('a')
        
        # Filter for article headlines
        headlines = []
        navigation_keywords = [
            "Home", "Trump", "U.S. News", "World", "Science", "Video", "Opinion", 
            "Investigations", "Ethics Policy", "About us", "Games", "HELP", 
            "get the newsletter", "JOIN RAW STORY", "Masthead", "Privacy Policy", 
            "Manage Preferences", "Debug Logs", "MORE", "Raw Exclusives",
            "Frontpage Commentary", "Also Read", "SmartNews", "Super Head",
            "Trump News", "Media"
        ]
        
        seen_headlines = set()  # To avoid duplicates
        
        for link in links:
            text = link.get_text().strip().replace('\n', ' ').replace('\r', '')
            href = link.get('href')
            
            # Only process links with both text and href
            if not text or not href:
                continue
                
            # Skip navigation/footer links
            if any(keyword in text for keyword in navigation_keywords):
                continue
                
            # Skip short navigation-style text (likely not article headlines)
            if len(text) < 20:
                continue
                
            # Skip mailto links and javascript links
            if href.startswith('mailto:') or href.startswith('javascript:'):
                continue
                
            # Skip relative links that are likely navigation (like "/", "/st/", etc.)
            if href.startswith('/') and len(href) < 20:
                continue
                
            # Look for article-like URLs (contain year/article ID patterns)
            if '/2674' in href or 'rawstory.com' in href:
                # This looks like an article headline
                if len(text) > 100:
                    text = text[:100] + "..."
                
                # Avoid duplicates
                if text not in seen_headlines:
                    headlines.append(text)
                    seen_headlines.add(text)
        
        return headlines[:20]  # Limit to first 20 unique headlines
        
    except requests.exceptions.RequestException as e:
        return [f"Network error: {str(e)}"]
    except Exception as e:
        return [f"Error scraping news: {str(e)}"]

def main():
    """Main function for command line usage"""
    if len(sys.argv) > 1:
        url = sys.argv[1]
    else:
        url = "https://rawstory.com"
    
    headlines = scrape_news_headlines(url)
    
    # Output as JSON for Open WebUI
    result = {
        "headlines": headlines,
        "count": len(headlines),
        "source": url
    }
    
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()
