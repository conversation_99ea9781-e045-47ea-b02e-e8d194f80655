╔════════════════════════════════════════════════════════════════════════════╗
║                    TABLET AUTHORIZATION STEP-BY-STEP                       ║
╚════════════════════════════════════════════════════════════════════════════╝

STEP 1: CONNECT USB CABLE
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  [Linux Computer]                    [Samsung Tablet]
        |                                    |
        |-------- USB Cable --------|
        |                                    |
        
  1. Plug USB cable into tablet
  2. Plug USB cable into Linux computer
  3. Wait 5-10 seconds


STEP 2: <PERSON>OOK AT TABLET SCREEN
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  You should see a dialog like this on your TABLET SCREEN:

  ┌─────────────────────────────────────────────────────┐
  │                                                     │
  │         Allow USB debugging?                        │
  │                                                     │
  │  The identity of this computer is:                 │
  │                                                     │
  │  RSA key fingerprint:                              │
  │  XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX  │
  │                                                     │
  │  ☐ Always allow from this computer                 │
  │                                                     │
  │                                                     │
  │              [Cancel]    [Allow]                    │
  │                                                     │
  └─────────────────────────────────────────────────────┘

  ⚠️  IMPORTANT: This dialog appears on the TABLET, not on your computer!


STEP 3: AUTHORIZE ON TABLET
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  On your TABLET SCREEN:

  1. Look at the dialog
  2. (Optional) Check the box: ☑ Always allow from this computer
  3. Tap the [Allow] button

  ✓ The dialog should disappear
  ✓ The tablet is now authorized


STEP 4: VERIFY ON LINUX
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  On your LINUX COMPUTER, run:

  $ adb devices

  You should see:

  List of devices attached
  XXXXXXXXXXXXXXXX    device

  ✓ If it says "device" → SUCCESS! Authorized!
  ✗ If it says "unauthorized" → Go back to Step 2
  ✗ If nothing shows → Check USB cable and try again


TROUBLESHOOTING
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  ❌ No dialog appears on tablet?
     → Reconnect USB cable
     → Try different USB port
     → Try different USB cable
     → Check USB mode on tablet (should be "File Transfer", not "Charging")

  ❌ Device shows "unauthorized"?
     → Check tablet screen for dialog
     → Tap "Allow"
     → Run: adb devices

  ❌ Device shows "offline"?
     → Run: adb kill-server && adb start-server
     → Reconnect USB cable

  ❌ Still not working?
     → Run: ./diagnose_tablet.sh
     → Check: TABLET_USB_SETUP.md


QUICK COMMANDS
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  # Check connection status
  adb devices

  # Restart ADB
  adb kill-server && adb start-server

  # Run diagnostics
  ./diagnose_tablet.sh

  # Check Python helper
  python3 tablet_usb_helper.py


WHAT TO DO AFTER AUTHORIZATION
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  Once authorized (adb devices shows "device"):

  1. Check device info:
     adb shell getprop ro.build.model

  2. Transfer a file:
     adb push test.txt /sdcard/

  3. Use Python helper:
     python3 tablet_usb_helper.py

  4. See examples:
     python3 tablet_examples.py 1


IMPORTANT NOTES
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  ⚠️  The authorization dialog appears on the TABLET SCREEN, not on Linux!
  ⚠️  You must physically look at your tablet to see and tap the dialog
  ⚠️  The dialog may appear briefly - watch your tablet screen carefully
  ⚠️  If you miss it, disconnect and reconnect the USB cable
  ⚠️  Check "Always allow" to avoid this step next time


STILL NEED HELP?
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  Read: AUTHORIZE_TABLET.md
  Read: TABLET_USB_SETUP.md
  Run:  ./diagnose_tablet.sh

╚════════════════════════════════════════════════════════════════════════════╝

