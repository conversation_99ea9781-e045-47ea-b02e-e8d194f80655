{"functions": [{"name": "scrape_news", "description": "Scrapes the latest news headlines from Raw Story or other news websites. Returns a formatted list of current news headlines.", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "The news website URL to scrape (optional, defaults to rawstory.com)", "default": "https://rawstory.com"}}, "required": []}}, {"name": "get_weather", "description": "Retrieves the current weather for a given city.", "parameters": {"type": "object", "properties": {"city": {"type": "string", "description": "The city to get the weather for"}}, "required": ["city"]}}, {"name": "search_wikipedia", "description": "Searches Wikipedia for a given query and returns a summary.", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "The search query for Wikipedia"}}, "required": ["query"]}}]}