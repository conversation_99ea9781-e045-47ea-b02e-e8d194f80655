╔════════════════════════════════════════════════════════════════════════════╗
║                    CHANGE USB MODE: CHARGING → FILE TRANSFER               ║
╚════════════════════════════════════════════════════════════════════════════╝

PROBLEM
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  Your tablet is in "Charging only" mode
  ✗ No MTP connection
  ✗ ADB not detecting device
  ✗ Can't transfer files


SOLUTION: CHANGE USB MODE
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

METHOD 1: NOTIFICATION PANEL (EASIEST)
──────────────────────────────────────

  On your TABLET:

  1. Look at the TOP of your tablet screen
     You should see a notification like:
     
     ┌─────────────────────────────────────┐
     │ 🔌 USB charging this device         │
     └─────────────────────────────────────┘

  2. SWIPE DOWN from the top
     (Drag your finger down from the top of the screen)

  3. The notification panel opens:
     
     ┌─────────────────────────────────────┐
     │ 🔌 USB charging this device         │
     │                                     │
     │ Tap to change USB options           │
     └─────────────────────────────────────┘

  4. TAP on the USB notification

  5. A menu appears:
     
     ┌─────────────────────────────────────┐
     │ USB connection mode                 │
     │                                     │
     │ ○ Charging only                     │
     │ ● File transfer (MTP)               │
     │ ○ PTP                               │
     │ ○ MIDI                              │
     │                                     │
     │ [OK]                                │
     └─────────────────────────────────────┘

  6. SELECT "File transfer" or "MTP"
     (It may already be selected - that's good!)

  7. TAP [OK] or [Done]

  ✓ USB mode changed!


METHOD 2: SETTINGS MENU
──────────────────────

  If the notification method doesn't work:

  On your TABLET:

  1. Open SETTINGS
  2. Look for:
     - "Connected devices"
     - "Device connection"
     - "USB"
     - "Storage"

  3. Find "USB connection mode" or "USB preferences"
  4. Change to "File transfer" or "MTP"
  5. Confirm


VERIFY ON LINUX
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  On your LINUX COMPUTER:

  $ adb devices

  Expected output:

  List of devices attached
  XXXXXXXXXXXXXXXX    device

  ✓ If it shows "device" → SUCCESS!
  ✗ If it shows nothing → Try restarting ADB:
    
    $ adb kill-server
    $ adb start-server
    $ adb devices


QUICK TEST
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  Once device shows as "device":

  # Get device model
  $ adb shell getprop ro.build.model

  # List files on tablet
  $ adb shell ls /sdcard/

  # Transfer a test file
  $ echo "test" > test.txt
  $ adb push test.txt /sdcard/

  # Check with Python helper
  $ python3 tablet_usb_helper.py


USB MODES EXPLAINED
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  Charging only
  ├─ Purpose: Just charges the tablet
  ├─ File transfer: ✗ NO
  ├─ ADB: ✗ NO
  └─ Use when: You only want to charge

  File transfer / MTP ✓ USE THIS
  ├─ Purpose: Transfer files and use ADB
  ├─ File transfer: ✓ YES
  ├─ ADB: ✓ YES
  └─ Use when: You want to transfer files or use ADB

  PTP
  ├─ Purpose: Picture transfer (limited)
  ├─ File transfer: ✓ LIMITED
  ├─ ADB: ✗ NO
  └─ Use when: Transferring photos from camera

  MIDI
  ├─ Purpose: Musical instrument connection
  ├─ File transfer: ✗ NO
  ├─ ADB: ✗ NO
  └─ Use when: Connecting MIDI devices


TROUBLESHOOTING
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  ❌ Still showing "Charging only"?
     1. Disconnect USB cable
     2. Wait 5 seconds
     3. Reconnect USB cable
     4. Immediately check notification panel
     5. Tap notification and select "File transfer"

  ❌ Device still not detected?
     $ ./diagnose_tablet.sh
     $ dmesg | tail -20

  ❌ Authorization dialog appears again?
     - Tap "Allow"
     - Check "Always allow from this computer"
     - Confirm


AFTER CHANGING USB MODE
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  1. Verify connection:
     $ python3 tablet_usb_helper.py

  2. Transfer files:
     $ adb push local_file.txt /sdcard/
     $ adb pull /sdcard/remote_file.txt

  3. Run commands:
     $ adb shell ls /sdcard/

  4. Use Python helper:
     $ python3 tablet_examples.py 1


IMPORTANT NOTES
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  ⚠️  The USB mode setting is on the TABLET, not on your computer!
  ⚠️  You must physically change it on your tablet screen
  ⚠️  The notification may appear briefly - watch carefully
  ⚠️  If you miss it, disconnect and reconnect the USB cable
  ⚠️  Different Samsung models may have slightly different menus


STILL NEED HELP?
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

  Read: CHANGE_USB_MODE.md
  Read: TABLET_USB_SETUP.md
  Run:  ./diagnose_tablet.sh

╚════════════════════════════════════════════════════════════════════════════╝

