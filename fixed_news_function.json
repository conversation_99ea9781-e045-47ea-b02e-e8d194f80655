{"name": "scrape_news", "description": "Scrapes the latest news headlines from Raw Story or other news websites.", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "The news website URL to scrape", "default": "https://rawstory.com"}}, "required": []}, "code": "import requests\nfrom bs4 import BeautifulSoup\n\nclass Tools:\n    def __init__(self):\n        pass\n    \n    def scrape_news(self, url: str = \"https://rawstory.com\") -> str:\n        \"\"\"\n        Scrapes the latest news headlines from Raw Story or other news websites.\n        \n        :param url: The news website URL to scrape\n        :return: Formatted list of news headlines\n        \"\"\"\n        try:\n            # Send an HTTP request to the URL\n            response = requests.get(url, timeout=10)\n            \n            if response.status_code != 200:\n                return f\"❌ Failed to retrieve page. Status code: {response.status_code}\"\n            \n            # Parse the HTML content\n            soup = BeautifulSoup(response.content, 'html.parser')\n            links = soup.find_all('a')\n            \n            # Filter for article headlines\n            headlines = []\n            navigation_keywords = [\n                \"Home\", \"Trump\", \"U.S. News\", \"World\", \"Science\", \"Video\", \"Opinion\", \n                \"Investigations\", \"Ethics Policy\", \"About us\", \"Games\", \"HELP\", \n                \"get the newsletter\", \"JOIN RAW STORY\", \"Masthead\", \"Privacy Policy\", \n                \"Manage Preferences\", \"Debug Logs\", \"MORE\", \"Raw Exclusives\",\n                \"Frontpage Commentary\", \"Also Read\", \"SmartNews\", \"Super Head\",\n                \"Trump News\", \"Media\"\n            ]\n            \n            seen_headlines = set()\n            \n            for link in links:\n                text = link.get_text().strip().replace('\\n', ' ').replace('\\r', '')\n                href = link.get('href')\n                \n                if not text or not href:\n                    continue\n                    \n                if any(keyword in text for keyword in navigation_keywords):\n                    continue\n                    \n                if len(text) < 20:\n                    continue\n                    \n                if href.startswith('mailto:') or href.startswith('javascript:'):\n                    continue\n                    \n                if href.startswith('/') and len(href) < 20:\n                    continue\n                    \n                if '/2674' in href or 'rawstory.com' in href:\n                    if len(text) > 100:\n                        text = text[:100] + \"...\"\n                    \n                    if text not in seen_headlines:\n                        headlines.append(text)\n                        seen_headlines.add(text)\n            \n            if not headlines:\n                return \"📰 No news headlines found.\"\n            \n            result = f\"📰 **Latest News Headlines:**\\n\\n\"\n            \n            for i, headline in enumerate(headlines[:10], 1):\n                result += f\"{i}. {headline}\\n\\n\"\n            \n            result += f\"*Found {len(headlines[:10])} headlines*\"\n            return result\n            \n        except Exception as e:\n            return f\"❌ Error scraping news: {str(e)}\"\n\ntools = Tools()\nresult = tools.scrape_news(url)\nreturn result"}