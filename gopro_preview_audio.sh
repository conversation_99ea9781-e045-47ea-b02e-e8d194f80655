#!/usr/bin/env bash
set -euo pipefail

# Simple GoPro HERO8/9/10 USB live preview WITH audio using VLC.
# Usage:
#   ./gopro_preview_audio.sh                # defaults: 1080p, linear FOV, 300ms cache
#   RES=720 FOV=wide CACHE_MS=150 ./gopro_preview_audio.sh
#
# Requires: sudo, gopro (helper script), vlc
#   The helper script was installed at /usr/local/sbin/gopro

RES=${RES:-1080}
FOV=${FOV:-linear}
CACHE_MS=${CACHE_MS:-300}

need() { command -v "$1" >/dev/null 2>&1 || { echo "Missing dependency: $1" >&2; exit 1; }; }
need sudo
need gopro
need vlc

# Pre-cache sudo
sudo -v

# Start/ensure webcam mode is active (non-interactive).
echo "[GoPro] Starting webcam mode (RES=${RES} FOV=${FOV})..."
sudo gopro webcam -n -r "${RES}" -f "${FOV}" >/tmp/gopro_webcam.log 2>&1 || true

# You can tweak caching lower for lower latency (e.g. 150ms). Higher cache can reduce stutter on weak links.
echo "[GoPro] Launching VLC preview with audio (cache=${CACHE_MS}ms)..."
exec vlc --network-caching="${CACHE_MS}" udp://@:8554

