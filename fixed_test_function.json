{"name": "test_tool", "description": "Simple test function to verify that custom functions are working in Open WebUI.", "parameters": {"type": "object", "properties": {"message": {"type": "string", "description": "Test message to return", "default": "Hello from Open WebUI!"}}, "required": []}, "code": "class Tools:\n    def __init__(self):\n        pass\n    \n    def test_tool(self, message: str = \"Hello from Open WebUI!\") -> str:\n        \"\"\"\n        Simple test function to verify the tool is working.\n        \n        :param message: Test message to return\n        :return: Confirmation message\n        \"\"\"\n        import datetime\n        now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n        return f\"✅ Function is working! Time: {now}\\nMessage: {message}\"\n\ntools = Tools()\nresult = tools.test_tool(message)\nreturn result"}