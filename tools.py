#!/usr/bin/env python3
"""
Open WebUI Tools Handler
This file contains all the tool functions that Open WebUI can call
"""

import requests
import json
import subprocess
from news_scraper_tool import scrape_news_headlines

def get_weather(city: str) -> str:
    """
    Retrieves the current weather for a given city.
    
    Args:
        city (str): The city to get the weather for
        
    Returns:
        str: Weather information for the city
    """
    try:
        # This is a placeholder - you can implement with your preferred weather API
        # For now, let's use a simple response
        return f"Weather information for {city} is not currently available. Please check your local weather service."
    except Exception as e:
        return f"Error getting weather for {city}: {str(e)}"

def search_wikipedia(query: str) -> str:
    """
    Searches Wikipedia for a given query.
    
    Args:
        query (str): The search query
        
    Returns:
        str: Wikipedia search results
    """
    try:
        # Wikipedia API endpoint
        url = "https://en.wikipedia.org/api/rest_v1/page/summary/" + query.replace(" ", "_")
        
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            title = data.get('title', 'No title')
            extract = data.get('extract', 'No summary available')
            page_url = data.get('content_urls', {}).get('desktop', {}).get('page', '')
            
            result = f"**{title}**\n\n{extract}"
            if page_url:
                result += f"\n\nRead more: {page_url}"
            
            return result
        else:
            return f"No Wikipedia article found for '{query}'"
            
    except Exception as e:
        return f"Error searching Wikipedia for '{query}': {str(e)}"

def scrape_news(url: str = "https://rawstory.com") -> str:
    """
    Scrapes the latest news headlines from Raw Story or other news websites.

    Args:
        url (str): The news website URL to scrape (optional, defaults to rawstory.com)

    Returns:
        str: Formatted list of news headlines
    """
    print(f"[DEBUG] scrape_news called with url: {url}")  # Debug log

    try:
        print("[DEBUG] Calling scrape_news_headlines...")  # Debug log
        headlines = scrape_news_headlines(url)
        print(f"[DEBUG] Got {len(headlines)} headlines")  # Debug log

        if not headlines:
            return "No news headlines found."

        # Format the headlines nicely
        result = f"**Latest News Headlines from {url}:**\n\n"

        for i, headline in enumerate(headlines[:10], 1):  # Limit to 10 for faster response
            result += f"{i}. {headline}\n\n"

        result += f"\n*Found {len(headlines[:10])} headlines*"

        print("[DEBUG] Returning formatted result")  # Debug log
        return result

    except Exception as e:
        error_msg = f"Error scraping news from {url}: {str(e)}"
        print(f"[DEBUG] Error: {error_msg}")  # Debug log
        return error_msg

# Tool registry for Open WebUI
TOOLS = {
    "get_weather": get_weather,
    "search_wikipedia": search_wikipedia,
    "scrape_news": scrape_news
}

def main():
    """
    Main function for testing tools locally
    """
    print("Testing news scraper tool...")
    result = scrape_news()
    print(result)

if __name__ == "__main__":
    main()
