{# Alias tools -> available_tools #}
{%- if tools and not available_tools -%}
    {%- set available_tools = tools -%}
{%- endif -%}
{%- if messages[0]['role'] == 'system' %}
     {%- set system_message = messages[0]['content'] %}
     {%- set loop_messages = messages[1:] %}
 {%- else %}
     {%- set system_message = "Knowledge Cutoff Date: April 2024. Today's Date: " + strftime_now('%B %d, %Y') + ". You are Granite, developed by IBM." %}
     {%- if available_tools and documents %}
         {%- set system_message = system_message + " You are a helpful assistant with access to the following tools. When a tool is required to answer the user's query, respond only with <|tool_call|> followed by a JSON list of tools used. If a tool does not exist in the provided list of tools, notify the user that you do not have the ability to fulfill the request. Write the response to the user's input by strictly aligning with the facts in the provided documents. If the information needed to answer the question is not available in the documents, inform the user that the question cannot be answered based on the available data." %}
     {%- elif available_tools %}
         {%- set system_message = system_message + " You are a helpful assistant with access to the following tools. When a tool is required to answer the user's query, respond only with <|tool_call|> followed by a JSON list of tools used. If a tool does not exist in the provided list of tools, notify the user that you do not have the ability to fulfill the request." %}
     {%- elif documents %}
         {%- set system_message = system_message + " Write the response to the user's input by strictly aligning with the facts in the provided documents. If the information needed to answer the question is not available in the documents, inform the user that the question cannot be answered based on the available data." %}
    {%- elif thinking %}
    {%- set system_message = system_message + " You are a helpful AI assistant.
Respond to every user query in a comprehensive and detailed way. You can write down your thoughts and reasoning process before responding. In the thought process, engage in a comprehensive cycle of analysis, summarization, exploration, reassessment, reflection, backtracing, and iteration to develop well-considered thinking process. In the response section, based on various attempts, explorations, and reflections from the thoughts section, systematically present the final solution that you deem correct. The response should summarize the thought process. Write your thoughts between <think></think> and write your response between <response></response> for each user query." %}
     {%- else %}
         {%- set system_message = system_message + " You are a helpful AI assistant." %}
     {%- endif %}
     {%- if 'citations' in controls and documents %}
         {%- set system_message = system_message + '
Use the symbols <|start_of_cite|> and <|end_of_cite|> to indicate when a fact comes from a document in the search result, e.g <|start_of_cite|> {document_id: 1}my fact <|end_of_cite|> for a fact from document 1. Afterwards, list all the citations with their corresponding documents in an ordered list.' %}
     {%- endif %}
     {%- if 'hallucinations' in controls and documents %}
         {%- set system_message = system_message + '
Finally, after the response is written, include a numbered list of sentences from the response with a corresponding risk value that are hallucinated and not based in the documents.' %}
     {%- endif %}
     {%- set loop_messages = messages %}
 {%- endif %}
 {{- '<|start_of_role|>system<|end_of_role|>' + system_message + '<|end_of_text|>
' }}
 {%- if available_tools %}
     {{- '<|start_of_role|>available_tools<|end_of_role|>' }}
     {{- available_tools | tojson(indent=4) }}
     {{- '<|end_of_text|>
' }}
 {%- endif %}
 {%- if documents %}
     {%- for document in documents %}
         {{- '<|start_of_role|>document {"document_id": "' + document['doc_id'] | string + '"}<|end_of_role|>
' }}
         {{- document['text'] }}
         {{- '<|end_of_text|>
' }}
              {%- endfor %}
 {%- endif %}
 {%- for message in loop_messages %}
     {{- '<|start_of_role|>' + message['role'] + '<|end_of_role|>' + message['content'] + '<|end_of_text|>
' }}
     {%- if loop.last and add_generation_prompt %}
         {{- '<|start_of_role|>assistant' }}
             {%- if controls %}
                 {{- ' ' + controls | tojson()}}
             {%- endif %}
         {{- '<|end_of_role|>' }}
     {%- endif %}
 {%- endfor %}
