"""
title: Simple Test
author: Jesus
version: 1.0
description: Simple test function to verify Open WebUI integration
"""

class Tools:
    def __init__(self):
        pass

    def hello_world(self, name: str = "World") -> str:
        """
        Simple hello world function.
        
        Args:
            name (str): Name to greet
            
        Returns:
            str: Greeting message
        """
        return f"Hello {name}! The function is working! 🎉"
