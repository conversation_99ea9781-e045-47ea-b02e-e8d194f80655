"""
Title: News Scraper
Author: Jesus
Version: 1.0
Description: Scrapes latest news headlines from Raw Story and other news websites
"""

import requests
from bs4 import BeautifulSoup
from typing import Optional
import asyncio

class Tools:
    def __init__(self):
        pass

    async def scrape_news(
        self, 
        url: Optional[str] = "https://rawstory.com"
    ) -> str:
        """
        Scrapes the latest news headlines from Raw Story or other news websites.
        
        :param url: The news website URL to scrape (optional, defaults to rawstory.com)
        :return: Formatted list of news headlines
        """
        try:
            print(f"[DEBUG] Scraping news from: {url}")
            
            # Send an HTTP request to the URL
            response = requests.get(url, timeout=10)
            
            # Check if the request was successful
            if response.status_code != 200:
                return f"❌ Failed to retrieve page. Status code: {response.status_code}"
            
            # Parse the HTML content using Beautiful Soup
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find all links on the webpage
            links = soup.find_all('a')
            
            # Filter for article headlines
            headlines = []
            navigation_keywords = [
                "Home", "Trump", "U.S. News", "World", "Science", "Video", "Opinion", 
                "Investigations", "Ethics Policy", "About us", "Games", "HELP", 
                "get the newsletter", "JOIN RAW STORY", "Masthead", "Privacy Policy", 
                "Manage Preferences", "Debug Logs", "MORE", "Raw Exclusives",
                "Frontpage Commentary", "Also Read", "SmartNews", "Super Head",
                "Trump News", "Media"
            ]
            
            seen_headlines = set()  # To avoid duplicates
            
            for link in links:
                text = link.get_text().strip().replace('\n', ' ').replace('\r', '')
                href = link.get('href')
                
                # Only process links with both text and href
                if not text or not href:
                    continue
                    
                # Skip navigation/footer links
                if any(keyword in text for keyword in navigation_keywords):
                    continue
                    
                # Skip short navigation-style text (likely not article headlines)
                if len(text) < 20:
                    continue
                    
                # Skip mailto links and javascript links
                if href.startswith('mailto:') or href.startswith('javascript:'):
                    continue
                    
                # Skip relative links that are likely navigation (like "/", "/st/", etc.)
                if href.startswith('/') and len(href) < 20:
                    continue
                    
                # Look for article-like URLs (contain year/article ID patterns)
                if '/2674' in href or 'rawstory.com' in href:
                    # This looks like an article headline
                    if len(text) > 100:
                        text = text[:100] + "..."
                    
                    # Avoid duplicates
                    if text not in seen_headlines:
                        headlines.append(text)
                        seen_headlines.add(text)
            
            # Format the headlines nicely
            if not headlines:
                return "📰 No news headlines found."
            
            result = f"📰 **Latest News Headlines from {url}:**\n\n"
            
            for i, headline in enumerate(headlines[:12], 1):  # Limit to 12 headlines
                result += f"{i}. {headline}\n\n"
            
            result += f"*Found {len(headlines[:12])} headlines*"
            
            print(f"[DEBUG] Successfully scraped {len(headlines)} headlines")
            return result
            
        except requests.exceptions.RequestException as e:
            error_msg = f"🌐 Network error: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            return error_msg
        except Exception as e:
            error_msg = f"❌ Error scraping news: {str(e)}"
            print(f"[DEBUG] {error_msg}")
            return error_msg

    async def test_tool(self, message: str = "Hello from news tool!") -> str:
        """
        Simple test function to verify the tool is working.
        
        :param message: Test message to return
        :return: Confirmation message
        """
        return f"✅ News tool is working! Message: {message}"
