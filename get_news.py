#!/usr/bin/env python3
"""
Simple news scraper - just run this directly!
Usage: python get_news.py
"""

import requests
from bs4 import BeautifulSoup

def get_news():
    """Get news headlines and print them nicely"""
    try:
        print("🔄 Fetching latest news headlines...")
        
        response = requests.get("https://rawstory.com", timeout=10)
        
        if response.status_code != 200:
            print(f"❌ Failed to get page: {response.status_code}")
            return
        
        soup = BeautifulSoup(response.content, 'html.parser')
        links = soup.find_all('a')
        
        headlines = []
        navigation_keywords = [
            "Home", "Trump", "U.S. News", "World", "Science", "Video", "Opinion", 
            "Investigations", "Ethics Policy", "About us", "Games", "HELP", 
            "get the newsletter", "JOIN RAW STORY", "Masthead", "Privacy Policy", 
            "Manage Preferences", "Debug Logs", "MORE", "Raw Exclusives",
            "Frontpage Commentary", "Also Read", "SmartNews", "Super Head",
            "Trump News", "Media"
        ]
        
        seen_headlines = set()
        
        for link in links:
            text = link.get_text().strip().replace('\n', ' ').replace('\r', '')
            href = link.get('href')
            
            if not text or not href:
                continue
                
            if any(keyword in text for keyword in navigation_keywords):
                continue
                
            if len(text) < 20:
                continue
                
            if href.startswith('mailto:') or href.startswith('javascript:'):
                continue
                
            if href.startswith('/') and len(href) < 20:
                continue
                
            if '/2674' in href or 'rawstory.com' in href:
                if len(text) > 100:
                    text = text[:100] + "..."
                
                if text not in seen_headlines:
                    headlines.append(text)
                    seen_headlines.add(text)
        
        if not headlines:
            print("📰 No headlines found")
            return
        
        print("\n📰 **LATEST NEWS HEADLINES:**\n")
        print("=" * 60)
        
        for i, headline in enumerate(headlines[:15], 1):
            print(f"{i:2d}. {headline}")
            print()
        
        print("=" * 60)
        print(f"Found {len(headlines)} total headlines")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    get_news()
