#!/usr/bin/env python3
"""
Samsung Tablet USB Communication Examples
Demonstrates how to use the tablet_usb_helper module
"""

from tablet_usb_helper import TabletUSBHelper
import os
import sys


def example_1_check_connection():
    """Example 1: Check tablet connection status"""
    print("\n" + "="*60)
    print("EXAMPLE 1: Check Connection Status")
    print("="*60)
    
    tablet = TabletUSBHelper()
    tablet.print_status()


def example_2_get_device_info():
    """Example 2: Get detailed device information"""
    print("\n" + "="*60)
    print("EXAMPLE 2: Get Device Information")
    print("="*60)
    
    tablet = TabletUSBHelper()
    
    if not tablet.is_device_connected():
        print("✗ No device connected")
        return
    
    print("\nDevice Information:")
    info = tablet.get_device_info()
    for key, value in info.items():
        print(f"  {key:20s}: {value}")


def example_3_file_transfer():
    """Example 3: Transfer files to/from tablet"""
    print("\n" + "="*60)
    print("EXAMPLE 3: File Transfer")
    print("="*60)
    
    tablet = TabletUSBHelper()
    
    if not tablet.is_device_connected():
        print("✗ No device connected")
        return
    
    # Create a test file
    test_file = "test_tablet.txt"
    with open(test_file, 'w') as f:
        f.write("Hello from Linux!\n")
        f.write("This is a test file.\n")
    
    print(f"\n1. Created local file: {test_file}")
    
    # Push to tablet
    remote_path = f"/sdcard/{test_file}"
    if tablet.push_file(test_file, remote_path):
        print(f"2. Pushed to tablet: {remote_path}")
        
        # Pull back
        pulled_file = f"pulled_{test_file}"
        if tablet.pull_file(remote_path, pulled_file):
            print(f"3. Pulled from tablet: {pulled_file}")
            
            # Verify
            with open(pulled_file, 'r') as f:
                content = f.read()
                print(f"4. Verified content:\n{content}")
            
            # Cleanup
            os.remove(test_file)
            os.remove(pulled_file)
            print("5. Cleaned up local files")


def example_4_shell_commands():
    """Example 4: Execute shell commands on tablet"""
    print("\n" + "="*60)
    print("EXAMPLE 4: Shell Commands")
    print("="*60)
    
    tablet = TabletUSBHelper()
    
    if not tablet.is_device_connected():
        print("✗ No device connected")
        return
    
    commands = [
        ("List /sdcard", "ls /sdcard/"),
        ("Get date", "date"),
        ("Get uptime", "uptime"),
        ("Get disk space", "df /sdcard/"),
        ("Get memory info", "cat /proc/meminfo | head -5"),
    ]
    
    for description, cmd in commands:
        print(f"\n{description}:")
        print(f"  Command: {cmd}")
        stdout, stderr = tablet.shell_command(cmd)
        if stdout:
            for line in stdout.strip().split('\n')[:3]:  # Show first 3 lines
                print(f"  {line}")
        if stderr:
            print(f"  Error: {stderr}")


def example_5_list_devices():
    """Example 5: List all connected devices"""
    print("\n" + "="*60)
    print("EXAMPLE 5: List Connected Devices")
    print("="*60)
    
    tablet = TabletUSBHelper()
    devices = tablet.get_devices()
    
    if not devices:
        print("No devices found")
        return
    
    print(f"\nFound {len(devices)} device(s):")
    for device_id, status in devices:
        symbol = "✓" if status == "device" else "✗"
        print(f"  {symbol} {device_id:30s} ({status})")


def example_6_restart_adb():
    """Example 6: Restart ADB daemon"""
    print("\n" + "="*60)
    print("EXAMPLE 6: Restart ADB Daemon")
    print("="*60)
    
    tablet = TabletUSBHelper()
    
    print("\nRestarting ADB daemon...")
    if tablet.restart_adb():
        print("✓ ADB restarted successfully")
        print("✓ Device is connected")
    else:
        print("✗ ADB restarted but no device connected")


def example_7_batch_operations():
    """Example 7: Batch operations"""
    print("\n" + "="*60)
    print("EXAMPLE 7: Batch Operations")
    print("="*60)
    
    tablet = TabletUSBHelper()
    
    if not tablet.is_device_connected():
        print("✗ No device connected")
        return
    
    print("\nPerforming batch operations...")
    
    # Create multiple test files
    files = []
    for i in range(3):
        filename = f"batch_test_{i}.txt"
        with open(filename, 'w') as f:
            f.write(f"Batch file {i}\n")
        files.append(filename)
    
    print(f"1. Created {len(files)} local files")
    
    # Push all files
    for filename in files:
        tablet.push_file(filename, f"/sdcard/{filename}")
    
    print(f"2. Pushed all files to tablet")
    
    # List files on tablet
    stdout, _ = tablet.shell_command("ls /sdcard/batch_test_*.txt")
    print(f"3. Files on tablet:\n{stdout}")
    
    # Cleanup
    for filename in files:
        os.remove(filename)
    
    print("4. Cleaned up local files")


def main():
    """Run all examples"""
    print("\n" + "="*60)
    print("SAMSUNG TABLET USB COMMUNICATION EXAMPLES")
    print("="*60)
    
    examples = [
        ("Check Connection", example_1_check_connection),
        ("Get Device Info", example_2_get_device_info),
        ("File Transfer", example_3_file_transfer),
        ("Shell Commands", example_4_shell_commands),
        ("List Devices", example_5_list_devices),
        ("Restart ADB", example_6_restart_adb),
        ("Batch Operations", example_7_batch_operations),
    ]
    
    print("\nAvailable examples:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"  {i}. {name}")
    
    print("\nUsage:")
    print("  python3 tablet_examples.py [example_number]")
    print("  python3 tablet_examples.py all")
    print("  python3 tablet_examples.py 1")
    
    if len(sys.argv) > 1:
        arg = sys.argv[1]
        if arg == "all":
            for name, func in examples:
                try:
                    func()
                except Exception as e:
                    print(f"Error in {name}: {e}")
        else:
            try:
                idx = int(arg) - 1
                if 0 <= idx < len(examples):
                    examples[idx][1]()
                else:
                    print(f"Invalid example number: {arg}")
            except ValueError:
                print(f"Invalid argument: {arg}")
    else:
        # Run first example by default
        example_1_check_connection()


if __name__ == '__main__':
    main()

