#!/usr/bin/env python3
"""
Simple Flask API for news scraping
Run this and call it from Open WebUI as an external tool
"""

from flask import Flask, jsonify, request
from news_scraper_tool import scrape_news_headlines

app = Flask(__name__)

@app.route('/news', methods=['GET'])
def get_news():
    """Get news headlines"""
    url = request.args.get('url', 'https://rawstory.com')
    
    try:
        headlines = scrape_news_headlines(url)
        return jsonify({
            'success': True,
            'headlines': headlines,
            'count': len(headlines),
            'source': url
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy'})

if __name__ == '__main__':
    print("Starting news scraper API...")
    print("Access at: http://localhost:5000/news")
    app.run(host='0.0.0.0', port=5000, debug=True)
